package jarinker.core;

import com.sun.tools.jdeps.JdepsFilter;
import java.util.HashSet;
import java.util.Set;

/**
 * Builder for creating JdepsFilter instances with various filtering options.
 * This class provides a convenient way to configure jdeps filtering options
 * that correspond to command-line arguments.
 *
 * <AUTHOR>
 */
public class JdepsFilterBuilder {

    // Filter options
    private String filterPattern = "";
    private String regex = "";
    private boolean filterSamePackage = true; // default behavior
    private boolean filterSameArchive = false;
    private boolean findJDKInternals = false;
    private boolean findMissingDeps = false;

    // Source filters
    private String includePattern = "";
    private Set<String> requires = new HashSet<>();
    private Set<String> targetPackages = new HashSet<>();

    /**
     * Set filter pattern for filtering dependencies.
     *
     * @param pattern regex pattern to filter dependencies
     * @return this builder
     */
    public JdepsFilterBuilder filterPattern(String pattern) {
        this.filterPattern = pattern != null ? pattern : "";
        return this;
    }

    /**
     * Set regex pattern for finding dependencies.
     *
     * @param regex regex pattern to find dependencies
     * @return this builder
     */
    public JdepsFilterBuilder regex(String regex) {
        this.regex = regex != null ? regex : "";
        return this;
    }

    /**
     * Enable/disable filtering dependencies within the same package.
     *
     * @param filter true to filter same package dependencies
     * @return this builder
     */
    public JdepsFilterBuilder filterSamePackage(boolean filter) {
        this.filterSamePackage = filter;
        return this;
    }

    /**
     * Enable/disable filtering dependencies within the same archive.
     *
     * @param filter true to filter same archive dependencies
     * @return this builder
     */
    public JdepsFilterBuilder filterSameArchive(boolean filter) {
        this.filterSameArchive = filter;
        return this;
    }

    /**
     * Enable/disable finding JDK internal dependencies.
     *
     * @param find true to find JDK internal dependencies
     * @return this builder
     */
    public JdepsFilterBuilder findJDKInternals(boolean find) {
        this.findJDKInternals = find;
        return this;
    }

    /**
     * Enable/disable finding missing dependencies.
     *
     * @param find true to find missing dependencies
     * @return this builder
     */
    public JdepsFilterBuilder findMissingDeps(boolean find) {
        this.findMissingDeps = find;
        return this;
    }

    /**
     * Set include pattern to restrict analysis to classes matching pattern.
     *
     * @param pattern regex pattern to include classes
     * @return this builder
     */
    public JdepsFilterBuilder includePattern(String pattern) {
        this.includePattern = pattern != null ? pattern : "";
        return this;
    }

    /**
     * Add a required module name for dependency analysis.
     *
     * @param moduleName module name to require
     * @return this builder
     */
    public JdepsFilterBuilder addRequire(String moduleName) {
        if (moduleName != null && !moduleName.isEmpty()) {
            this.requires.add(moduleName);
        }
        return this;
    }

    /**
     * Add a target package for dependency analysis.
     *
     * @param packageName package name to target
     * @return this builder
     */
    public JdepsFilterBuilder addTargetPackage(String packageName) {
        if (packageName != null && !packageName.isEmpty()) {
            this.targetPackages.add(packageName);
        }
        return this;
    }

    /**
     * Build the JdepsFilter with the configured options.
     * Note: This is a simplified implementation that may not support all jdeps options
     * due to limitations in the internal jdeps API.
     *
     * @return configured JdepsFilter
     */
    public JdepsFilter build() {
        var filterBuilder = new JdepsFilter.Builder();

        // Apply basic filter settings
        filterBuilder.filter(filterSamePackage, filterSameArchive);

        // Apply missing dependencies option
        if (findMissingDeps) {
            filterBuilder.findMissingDeps(true);
        }

        // Note: Some options like filterPattern, regex, includePattern, requires, and targetPackages
        // may not be directly supported by the internal JdepsFilter.Builder API.
        // These would need to be implemented based on the actual available methods.

        return filterBuilder.build();
    }

    /**
     * Check if any custom filter options have been set.
     *
     * @return true if custom options are configured
     */
    public boolean hasCustomOptions() {
        return !filterPattern.isEmpty()
                || !regex.isEmpty()
                || !filterSamePackage
                || filterSameArchive
                || findJDKInternals
                || findMissingDeps
                || !includePattern.isEmpty()
                || !requires.isEmpty()
                || !targetPackages.isEmpty();
    }
}
